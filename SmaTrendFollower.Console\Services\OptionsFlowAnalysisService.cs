using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time options flow analysis service implementation
/// Leverages Polygon Developer subscription for enhanced options intelligence
/// </summary>
public sealed class OptionsFlowAnalysisService : IOptionsFlowAnalysisService, IDisposable
{
    private readonly IPolygonClientFactory _polygonFactory;
    private readonly IMarketDataService _marketDataService;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly ITickStreamService _tickStreamService;
    private readonly ILogger<OptionsFlowAnalysisService> _logger;
    private readonly OptionsFlowConfig _config;
    
    private readonly ConcurrentDictionary<string, OptionsFlowAnalysis> _flowCache = new();
    private readonly ConcurrentDictionary<string, List<UnusualOptionsContract>> _unusualActivityCache = new();
    private readonly HashSet<string> _monitoredSymbols = new();
    private readonly SemaphoreSlim _analysisLock = new(1, 1);
    
    private OptionsFlowSentiment _currentSentiment = OptionsFlowSentiment.Neutral;
    private bool _disposed;
    private Timer? _analysisTimer;

    // Redis key patterns
    private const string FlowCacheKeyPattern = "options_flow:analysis:{0}:{1:yyyyMMdd}";
    private const string UnusualActivityKeyPattern = "options_flow:unusual:{0}:{1:yyyyMMdd}";
    private const string PutCallRatioKeyPattern = "options_flow:pcr:{0}";
    private const string GammaExposureKeyPattern = "options_flow:gamma:{0}";

    public OptionsFlowAnalysisService(
        IPolygonClientFactory polygonFactory,
        IMarketDataService marketDataService,
        IOptimizedRedisConnectionService redisService,
        ITickStreamService tickStreamService,
        IConfiguration configuration,
        ILogger<OptionsFlowAnalysisService> logger)
    {
        _polygonFactory = polygonFactory ?? throw new ArgumentNullException(nameof(polygonFactory));
        _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _tickStreamService = tickStreamService ?? throw new ArgumentNullException(nameof(tickStreamService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _config = new OptionsFlowConfig();
        configuration.GetSection("OptionsFlow").Bind(_config);
    }

    // === Events ===
    
    public event EventHandler<UnusualOptionsActivityEventArgs>? UnusualActivityDetected;
    public event EventHandler<OptionsFlowSentimentEventArgs>? SentimentChanged;

#pragma warning disable CS0067 // Event is never used - reserved for future implementation
    public event EventHandler<LargeBlockTradeEventArgs>? LargeBlockDetected;
#pragma warning restore CS0067

    // === Properties ===
    
    public OptionsFlowSentiment CurrentSentiment => _currentSentiment;
    public int MonitoredSymbols => _monitoredSymbols.Count;

    // === Analysis Methods ===

    public async Task<OptionsFlowAnalysis> AnalyzeOptionsFlowAsync(string symbol, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(OptionsFlowAnalysisService));

        await _analysisLock.WaitAsync(cancellationToken);
        try
        {
            // Check cache first
            var cacheKey = $"{symbol}:{DateTime.Today:yyyyMMdd}";
            if (_flowCache.TryGetValue(cacheKey, out var cached) && IsAnalysisValid(cached))
            {
                return cached;
            }

            _logger.LogInformation("Analyzing options flow for {Symbol}", symbol);

            // Perform parallel analysis
            var putCallTask = GetPutCallRatioAsync(symbol, cancellationToken);
            var unusualVolumeTask = DetectUnusualVolumeAsync(symbol, cancellationToken);
            var gammaExposureTask = AnalyzeGammaExposureAsync(symbol, cancellationToken);
            var darkPoolTask = AnalyzeDarkPoolActivityAsync(symbol, cancellationToken);

            await Task.WhenAll(putCallTask, unusualVolumeTask, gammaExposureTask, darkPoolTask);

            var putCallRatio = await putCallTask;
            var unusualVolume = await unusualVolumeTask;
            var gammaExposure = await gammaExposureTask;
            var darkPool = await darkPoolTask;

            // Calculate overall flow score
            var flowScore = CalculateFlowScore(putCallRatio, unusualVolume, gammaExposure, darkPool);
            
            // Determine sentiment
            var sentiment = DetermineSentiment(flowScore, putCallRatio, unusualVolume);
            
            // Generate analysis and key signals
            var (analysis, keySignals) = GenerateAnalysisAndSignals(symbol, sentiment, putCallRatio, unusualVolume, gammaExposure, darkPool);

            var flowAnalysis = new OptionsFlowAnalysis(
                symbol,
                DateTime.UtcNow,
                sentiment,
                putCallRatio,
                unusualVolume,
                gammaExposure,
                darkPool,
                flowScore,
                analysis,
                keySignals
            );

            // Cache the result
            _flowCache.TryAdd(cacheKey, flowAnalysis);
            await CacheFlowAnalysisAsync(flowAnalysis);

            // Check for sentiment changes
            if (sentiment != _currentSentiment)
            {
                var previousSentiment = _currentSentiment;
                _currentSentiment = sentiment;
                
                SentimentChanged?.Invoke(this, new OptionsFlowSentimentEventArgs
                {
                    PreviousSentiment = previousSentiment,
                    CurrentSentiment = sentiment,
                    ChangeTime = DateTime.UtcNow,
                    Reason = $"Flow score: {flowScore:F1}, P/C ratio: {putCallRatio.CurrentRatio:F2}"
                });
            }

            // Check for unusual activity alerts
            if (unusualVolume.IsUnusual)
            {
                UnusualActivityDetected?.Invoke(this, new UnusualOptionsActivityEventArgs
                {
                    Symbol = symbol,
                    Analysis = unusualVolume,
                    DetectedAt = DateTime.UtcNow,
                    AlertMessage = $"Unusual options volume detected: {unusualVolume.VolumeMultiplier:F1}x average"
                });
            }

            return flowAnalysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing options flow for {Symbol}", symbol);
            
            // Return safe default
            return new OptionsFlowAnalysis(
                symbol,
                DateTime.UtcNow,
                OptionsFlowSentiment.Neutral,
                new PutCallRatioAnalysis(1.0m, 1.0m, 0m, false, "Error"),
                new UnusualVolumeAnalysis(0, 0, 0m, false, new List<UnusualOptionsContract>()),
                new GammaExposureAnalysis(0m, 0m, 0m, false, new Dictionary<decimal, decimal>(), "Error"),
                new DarkPoolAnalysis(0m, 0m, 0m, false, new List<LargeBlockTrade>(), "Error"),
                50m,
                $"Analysis error: {ex.Message}",
                new List<string> { "Analysis failed" }
            );
        }
        finally
        {
            _analysisLock.Release();
        }
    }

    public async Task<PutCallRatioAnalysis> GetPutCallRatioAsync(string symbol, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get options data from Polygon
            var httpClient = _polygonFactory.CreateClient();
            var today = DateTime.Today;
            
            // Get options trades for today
            var url = $"/v3/trades/{symbol}?timestamp.gte={((DateTimeOffset)today).ToUnixTimeMilliseconds()}&limit=1000";
            url = _polygonFactory.AddApiKeyToUrl(url);

            var response = await httpClient.GetAsync(url, cancellationToken);
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var tradesResponse = JsonSerializer.Deserialize<PolygonTradesResponse>(content);

            if (tradesResponse?.Results == null || !tradesResponse.Results.Any())
            {
                return new PutCallRatioAnalysis(1.0m, 1.0m, 0m, false, "No data available");
            }

            // Calculate put/call volume ratio
            var putVolume = 0L;
            var callVolume = 0L;

            foreach (var trade in tradesResponse.Results)
            {
                // Determine if it's a put or call based on option symbol format
                if (!string.IsNullOrEmpty(trade.Symbol) && IsOptionSymbol(trade.Symbol))
                {
                    if (trade.Symbol.Contains('P'))
                        putVolume += trade.Size;
                    else if (trade.Symbol.Contains('C'))
                        callVolume += trade.Size;
                }
            }

            var currentRatio = callVolume > 0 ? (decimal)putVolume / callVolume : 0m;
            
            // Get historical average (simplified - would use more sophisticated calculation in production)
            var averageRatio = await GetHistoricalPutCallRatioAsync(symbol);
            var zScore = averageRatio > 0 ? (currentRatio - averageRatio) / (averageRatio * 0.3m) : 0m; // Assume 30% std dev
            var isExtreme = Math.Abs(zScore) > 2.0m;

            var interpretation = zScore switch
            {
                > 2.0m => "Extremely bearish - high put buying",
                > 1.0m => "Bearish - elevated put activity",
                < -2.0m => "Extremely bullish - high call buying",
                < -1.0m => "Bullish - elevated call activity",
                _ => "Neutral - balanced put/call activity"
            };

            return new PutCallRatioAnalysis(currentRatio, averageRatio, zScore, isExtreme, interpretation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating put/call ratio for {Symbol}", symbol);
            return new PutCallRatioAnalysis(1.0m, 1.0m, 0m, false, $"Error: {ex.Message}");
        }
    }

    public async Task<UnusualVolumeAnalysis> DetectUnusualVolumeAsync(string symbol, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get current options volume
            var optionsData = await _marketDataService.GetOptionsDataAsync(symbol);
            var currentVolume = optionsData.Sum(o => o.Volume ?? 0);
            
            // Get historical average volume (simplified calculation)
            var averageVolume = await GetHistoricalAverageVolumeAsync(symbol);
            var volumeMultiplier = averageVolume > 0 ? (decimal)currentVolume / averageVolume : 0m;
            var isUnusual = volumeMultiplier > _config.UnusualVolumeThreshold;

            // Find unusual individual contracts
            var unusualContracts = optionsData
                .Where(o => o.Volume.HasValue && o.Volume > _config.MinUnusualContractVolume)
                .Select(o => new UnusualOptionsContract(
                    o.Symbol,
                    o.OptionType,
                    o.Strike,
                    o.ExpirationDate,
                    o.Volume ?? 0,
                    100, // Simplified average volume
                    o.Volume.HasValue ? (decimal)o.Volume.Value / 100m : 0m,
                    o.LastPrice ?? 0m,
                    o.ImpliedVolatility ?? 0m
                ))
                .Where(c => c.VolumeMultiplier > _config.UnusualVolumeThreshold)
                .OrderByDescending(c => c.VolumeMultiplier)
                .Take(10)
                .ToList();

            return new UnusualVolumeAnalysis(currentVolume, averageVolume, volumeMultiplier, isUnusual, unusualContracts);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting unusual volume for {Symbol}", symbol);
            return new UnusualVolumeAnalysis(0, 0, 0m, false, new List<UnusualOptionsContract>());
        }
    }

    public async Task<GammaExposureAnalysis> AnalyzeGammaExposureAsync(string symbol, CancellationToken cancellationToken = default)
    {
        try
        {
            var optionsData = await _marketDataService.GetOptionsDataAsync(symbol);
            var currentPrice = (await _marketDataService.GetStockBarsAsync(symbol, DateTime.Today.AddDays(-1), DateTime.Today))
                .Items.LastOrDefault()?.Close ?? 0m;

            var gammaByStrike = new Dictionary<decimal, decimal>();
            var totalGammaExposure = 0m;
            var netGammaExposure = 0m;

            foreach (var option in optionsData.Where(o => o.Gamma.HasValue && o.Volume.HasValue))
            {
                var gamma = option.Gamma!.Value;
                var volume = option.Volume!.Value;
                var notionalGamma = gamma * volume * 100m; // 100 shares per contract

                if (!gammaByStrike.ContainsKey(option.Strike))
                    gammaByStrike[option.Strike] = 0m;

                if (option.OptionType.ToLower() == "call")
                {
                    gammaByStrike[option.Strike] += notionalGamma;
                    totalGammaExposure += Math.Abs(notionalGamma);
                    netGammaExposure += notionalGamma;
                }
                else // put
                {
                    gammaByStrike[option.Strike] -= notionalGamma;
                    totalGammaExposure += Math.Abs(notionalGamma);
                    netGammaExposure -= notionalGamma;
                }
            }

            // Find gamma flip level (where net gamma changes sign)
            var gammaFlipLevel = FindGammaFlipLevel(gammaByStrike, currentPrice);
            var isAboveGammaFlip = currentPrice > gammaFlipLevel;

            var marketImpact = isAboveGammaFlip 
                ? "Above gamma flip - market makers buying on dips, selling on rallies"
                : "Below gamma flip - market makers selling on dips, buying on rallies";

            return new GammaExposureAnalysis(
                totalGammaExposure,
                netGammaExposure,
                gammaFlipLevel,
                isAboveGammaFlip,
                gammaByStrike,
                marketImpact
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing gamma exposure for {Symbol}", symbol);
            return new GammaExposureAnalysis(0m, 0m, 0m, false, new Dictionary<decimal, decimal>(), $"Error: {ex.Message}");
        }
    }

    public async Task<DarkPoolAnalysis> AnalyzeDarkPoolActivityAsync(string symbol, CancellationToken cancellationToken = default)
    {
        try
        {
            // Get recent trades from Polygon
            var httpClient = _polygonFactory.CreateClient();
            var today = DateTime.Today;
            var url = $"/v3/trades/{symbol}?timestamp.gte={((DateTimeOffset)today).ToUnixTimeMilliseconds()}&limit=1000";
            url = _polygonFactory.AddApiKeyToUrl(url);

            var response = await httpClient.GetAsync(url, cancellationToken);
            response.EnsureSuccessStatusCode();

            var content = await response.Content.ReadAsStringAsync(cancellationToken);
            var tradesResponse = JsonSerializer.Deserialize<PolygonTradesResponse>(content);

            if (tradesResponse?.Results == null || !tradesResponse.Results.Any())
            {
                return new DarkPoolAnalysis(0m, 0m, 0m, false, new List<LargeBlockTrade>(), "No trade data available");
            }

            var darkPoolVolume = 0L;
            var totalVolume = 0L;
            var largeBlocks = new List<LargeBlockTrade>();

            foreach (var trade in tradesResponse.Results)
            {
                totalVolume += trade.Size;
                
                // Identify dark pool trades (simplified - would use more sophisticated logic)
                var isDarkPool = !string.IsNullOrEmpty(trade.Exchange) && IsDarkPoolTrade(trade.Exchange);
                if (isDarkPool)
                    darkPoolVolume += trade.Size;

                // Identify large block trades
                if (trade.Size >= _config.LargeBlockThreshold)
                {
                    largeBlocks.Add(new LargeBlockTrade(
                        symbol,
                        trade.Price,
                        trade.Size,
                        DateTimeOffset.FromUnixTimeMilliseconds(trade.ParticipantTimestamp).DateTime,
                        trade.Exchange,
                        isDarkPool,
                        trade.Price * trade.Size
                    ));
                }
            }

            var darkPoolPercentage = totalVolume > 0 ? (decimal)darkPoolVolume / totalVolume * 100m : 0m;
            var isHighDarkPoolActivity = darkPoolPercentage > _config.HighDarkPoolThreshold;

            var analysis = darkPoolPercentage switch
            {
                > 50m => "Very high dark pool activity - institutional accumulation/distribution",
                > 30m => "High dark pool activity - significant institutional interest",
                > 15m => "Moderate dark pool activity - some institutional flow",
                _ => "Low dark pool activity - mostly retail flow"
            };

            return new DarkPoolAnalysis(
                darkPoolVolume,
                totalVolume,
                darkPoolPercentage,
                isHighDarkPoolActivity,
                largeBlocks.OrderByDescending(b => b.NotionalValue).Take(10).ToList(),
                analysis
            );
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing dark pool activity for {Symbol}", symbol);
            return new DarkPoolAnalysis(0m, 0m, 0m, false, new List<LargeBlockTrade>(), $"Error: {ex.Message}");
        }
    }

    // === Monitoring Methods ===

    public async Task StartMonitoringAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        var symbolList = symbols.ToList();
        _logger.LogInformation("Starting options flow monitoring for {Count} symbols", symbolList.Count);

        foreach (var symbol in symbolList)
        {
            _monitoredSymbols.Add(symbol);
        }

        // Start periodic analysis timer
        _analysisTimer = new Timer(PeriodicAnalysis, null, TimeSpan.Zero, TimeSpan.FromMinutes(_config.AnalysisIntervalMinutes));

        await Task.CompletedTask;
    }

    public async Task StopMonitoringAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Stopping options flow monitoring");
        
        _analysisTimer?.Dispose();
        _analysisTimer = null;
        _monitoredSymbols.Clear();

        await Task.CompletedTask;
    }

    public async Task AddSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        foreach (var symbol in symbols)
        {
            _monitoredSymbols.Add(symbol);
        }
        await Task.CompletedTask;
    }

    public async Task RemoveSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        foreach (var symbol in symbols)
        {
            _monitoredSymbols.Remove(symbol);
        }
        await Task.CompletedTask;
    }

    // === Historical Analysis ===

    public async Task<OptionsFlowSummary> GetHistoricalFlowSummaryAsync(string symbol, DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        // Implementation would fetch historical data and analyze patterns
        // For now, return a placeholder
        return new OptionsFlowSummary(
            symbol,
            startDate,
            endDate,
            0L,
            1.0m,
            0,
            0m,
            new List<SignificantFlowEvent>()
        );
    }

    public async Task<FlowCorrelationAnalysis> GetFlowCorrelationAsync(string symbol, TimeSpan lookbackPeriod, CancellationToken cancellationToken = default)
    {
        // Implementation would analyze correlation between flow and price movements
        // For now, return a placeholder
        return new FlowCorrelationAnalysis(
            0m,
            0m,
            TimeSpan.Zero,
            new Dictionary<string, decimal>(),
            "Analysis not yet implemented"
        );
    }

    // === Private Methods ===

    private bool IsAnalysisValid(OptionsFlowAnalysis analysis)
    {
        return DateTime.UtcNow - analysis.AnalysisTime < TimeSpan.FromMinutes(_config.CacheExpiryMinutes);
    }

    private decimal CalculateFlowScore(PutCallRatioAnalysis putCall, UnusualVolumeAnalysis volume, GammaExposureAnalysis gamma, DarkPoolAnalysis darkPool)
    {
        var score = 50m; // Neutral baseline

        // Put/call ratio impact
        score += putCall.ZScore * -10m; // Negative because high P/C is bearish

        // Volume impact
        if (volume.IsUnusual)
            score += volume.VolumeMultiplier > 3m ? 15m : 10m;

        // Gamma impact
        if (gamma.IsAboveGammaFlip)
            score += 5m;
        else
            score -= 5m;

        // Dark pool impact
        if (darkPool.IsHighDarkPoolActivity)
            score += darkPool.DarkPoolPercentage > 40m ? 10m : 5m;

        return Math.Max(0m, Math.Min(100m, score));
    }

    private OptionsFlowSentiment DetermineSentiment(decimal flowScore, PutCallRatioAnalysis putCall, UnusualVolumeAnalysis volume)
    {
        return flowScore switch
        {
            >= 80m => OptionsFlowSentiment.ExtremeGreed,
            >= 65m => OptionsFlowSentiment.Greed,
            >= 35m => OptionsFlowSentiment.Neutral,
            >= 20m => OptionsFlowSentiment.Fear,
            _ => OptionsFlowSentiment.ExtremeFear
        };
    }

    private (string analysis, List<string> keySignals) GenerateAnalysisAndSignals(
        string symbol, OptionsFlowSentiment sentiment, PutCallRatioAnalysis putCall, 
        UnusualVolumeAnalysis volume, GammaExposureAnalysis gamma, DarkPoolAnalysis darkPool)
    {
        var signals = new List<string>();
        
        if (putCall.IsExtreme)
            signals.Add($"Extreme P/C ratio: {putCall.CurrentRatio:F2} ({putCall.Interpretation})");
        
        if (volume.IsUnusual)
            signals.Add($"Unusual volume: {volume.VolumeMultiplier:F1}x average");
        
        if (gamma.IsAboveGammaFlip)
            signals.Add("Above gamma flip level - supportive flow");
        else
            signals.Add("Below gamma flip level - volatile conditions");
        
        if (darkPool.IsHighDarkPoolActivity)
            signals.Add($"High dark pool activity: {darkPool.DarkPoolPercentage:F1}%");

        var analysis = $"Options flow sentiment: {sentiment}. " +
                      $"P/C ratio: {putCall.CurrentRatio:F2} (Z-score: {putCall.ZScore:F1}). " +
                      $"Volume: {volume.VolumeMultiplier:F1}x average. " +
                      $"Gamma exposure: {gamma.NetGammaExposure:F0}. " +
                      $"Dark pool: {darkPool.DarkPoolPercentage:F1}%.";

        return (analysis, signals);
    }

    private decimal FindGammaFlipLevel(Dictionary<decimal, decimal> gammaByStrike, decimal currentPrice)
    {
        // Find the strike where cumulative gamma changes from positive to negative
        var sortedStrikes = gammaByStrike.Keys.OrderBy(k => k).ToList();
        var cumulativeGamma = 0m;
        
        foreach (var strike in sortedStrikes)
        {
            cumulativeGamma += gammaByStrike[strike];
            if (cumulativeGamma <= 0m)
                return strike;
        }
        
        return currentPrice; // Default to current price if no flip found
    }

    private bool IsOptionSymbol(string symbol)
    {
        // Simplified option symbol detection
        return symbol.Length > 10 && (symbol.Contains('C') || symbol.Contains('P'));
    }

    private bool IsDarkPoolTrade(string exchange)
    {
        // Simplified dark pool detection based on exchange codes
        var darkPoolExchanges = new[] { "D", "X", "Y", "TRF" };
        return darkPoolExchanges.Contains(exchange);
    }

    private async Task<decimal> GetHistoricalPutCallRatioAsync(string symbol)
    {
        // Simplified - would implement proper historical calculation
        return 1.0m;
    }

    private async Task<long> GetHistoricalAverageVolumeAsync(string symbol)
    {
        // Simplified - would implement proper historical calculation
        return 1000L;
    }

    private async Task CacheFlowAnalysisAsync(OptionsFlowAnalysis analysis)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(FlowCacheKeyPattern, analysis.Symbol, analysis.AnalysisTime.Date);
            var json = JsonSerializer.Serialize(analysis);
            await database.StringSetAsync(key, json, TimeSpan.FromHours(24));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cache flow analysis for {Symbol}", analysis.Symbol);
        }
    }

    private async void PeriodicAnalysis(object? state)
    {
        if (_disposed || !_monitoredSymbols.Any())
            return;

        try
        {
            var tasks = _monitoredSymbols.Take(10).Select(symbol => 
                AnalyzeOptionsFlowAsync(symbol, CancellationToken.None));
            
            await Task.WhenAll(tasks);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in periodic options flow analysis");
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _analysisTimer?.Dispose();
        _analysisLock?.Dispose();
        _disposed = true;
    }
}

/// <summary>
/// Configuration for options flow analysis
/// </summary>
public class OptionsFlowConfig
{
    public decimal UnusualVolumeThreshold { get; set; } = 2.0m;
    public long MinUnusualContractVolume { get; set; } = 100L;
    public long LargeBlockThreshold { get; set; } = 10000L;
    public decimal HighDarkPoolThreshold { get; set; } = 25.0m;
    public int AnalysisIntervalMinutes { get; set; } = 5;
    public int CacheExpiryMinutes { get; set; } = 15;
}
