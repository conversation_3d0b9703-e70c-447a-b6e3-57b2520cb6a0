using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Microstructure pattern detector implementation
/// Detects uptick + bid support and down-tick + spread widening patterns
/// Provides optimal entry timing for volatile environments
/// </summary>
public sealed class MicrostructurePatternDetector : IMicrostructurePatternDetector, IDisposable
{
    private readonly ITickStreamService _tickStreamService;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly ILogger<MicrostructurePatternDetector> _logger;
    
    private readonly ConcurrentDictionary<string, MicrostructureTracker> _microstructureTrackers = new();
    private readonly HashSet<string> _monitoredSymbols = new();
    private readonly object _symbolsLock = new();
    
    private MicrostructurePatternConfig _config;
    private MicrostructureDetectionStatus _status = MicrostructureDetectionStatus.Stopped;
    private bool _disposed;
    
    // Redis key patterns
    private const string MicrostructureAnalysisKeyPattern = "microstructure:analysis:{0}";
    private const string PatternHistoryKeyPattern = "microstructure:patterns:{0}";
    
    public event EventHandler<UptickBidSupportEventArgs>? UptickBidSupportDetected;
    public event EventHandler<DowntickSpreadWideningEventArgs>? DowntickSpreadWideningDetected;
    public event EventHandler<OptimalEntryEventArgs>? OptimalEntryDetected;
    public event EventHandler<MicrostructureDeteriorationEventArgs>? MicrostructureDeterioration;
    
    public MicrostructurePatternDetector(
        ITickStreamService tickStreamService,
        IOptimizedRedisConnectionService redisService,
        IConfiguration configuration,
        ILogger<MicrostructurePatternDetector> logger)
    {
        _tickStreamService = tickStreamService ?? throw new ArgumentNullException(nameof(tickStreamService));
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // Load configuration
        _config = LoadConfiguration(configuration);
        
        // Subscribe to tick stream events
        _tickStreamService.TradeReceived += OnTradeReceived;
        _tickStreamService.QuoteReceived += OnQuoteReceived;
        
        _logger.LogInformation("MicrostructurePatternDetector initialized with {SequenceLength} tick sequence, {MinUptick}% min uptick",
            _config.TickSequenceLength, _config.MinUptickThreshold);
    }
    
    public async Task StartDetectionAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(MicrostructurePatternDetector));
            
        var symbolList = symbols.ToList();
        if (!symbolList.Any())
            return;
            
        try
        {
            _status = MicrostructureDetectionStatus.Starting;
            _logger.LogInformation("Starting microstructure pattern detection for {Count} symbols", symbolList.Count);
            
            // Initialize microstructure trackers
            foreach (var symbol in symbolList)
            {
                var tracker = new MicrostructureTracker(symbol, _config, _logger);
                _microstructureTrackers.TryAdd(symbol, tracker);
            }
            
            // Update monitored symbols
            lock (_symbolsLock)
            {
                foreach (var symbol in symbolList)
                {
                    _monitoredSymbols.Add(symbol);
                }
            }
            
            // Subscribe to tick stream
            await _tickStreamService.SubscribeAsync(symbolList, TickDataTypes.Trades | TickDataTypes.Quotes, cancellationToken);
            
            _status = MicrostructureDetectionStatus.Active;
            _logger.LogInformation("Microstructure pattern detection started successfully for {Count} symbols", symbolList.Count);
        }
        catch (Exception ex)
        {
            _status = MicrostructureDetectionStatus.Error;
            _logger.LogError(ex, "Error starting microstructure pattern detection");
            throw;
        }
    }
    
    public async Task StopDetectionAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;
            
        try
        {
            _logger.LogInformation("Stopping microstructure pattern detection");
            
            // Clear monitored symbols
            lock (_symbolsLock)
            {
                _monitoredSymbols.Clear();
            }
            
            // Clear trackers
            _microstructureTrackers.Clear();
            
            _status = MicrostructureDetectionStatus.Stopped;
            _logger.LogInformation("Microstructure pattern detection stopped");
        }
        catch (Exception ex)
        {
            _status = MicrostructureDetectionStatus.Error;
            _logger.LogError(ex, "Error stopping microstructure pattern detection");
            throw;
        }
    }
    
    public async Task AddSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(MicrostructurePatternDetector));
            
        var symbolList = symbols.ToList();
        if (!symbolList.Any())
            return;
            
        var newSymbols = new List<string>();
        
        lock (_symbolsLock)
        {
            foreach (var symbol in symbolList)
            {
                if (_monitoredSymbols.Add(symbol))
                {
                    newSymbols.Add(symbol);
                }
            }
        }
        
        if (newSymbols.Any())
        {
            foreach (var symbol in newSymbols)
            {
                var tracker = new MicrostructureTracker(symbol, _config, _logger);
                _microstructureTrackers.TryAdd(symbol, tracker);
            }
            
            if (_status == MicrostructureDetectionStatus.Active)
            {
                await _tickStreamService.SubscribeAsync(newSymbols, TickDataTypes.Trades | TickDataTypes.Quotes, cancellationToken);
            }
            
            _logger.LogInformation("Added {Count} new symbols to microstructure pattern detection", newSymbols.Count);
        }
    }
    
    public async Task RemoveSymbolsAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            return;
            
        var symbolList = symbols.ToList();
        if (!symbolList.Any())
            return;
            
        var removedSymbols = new List<string>();
        
        lock (_symbolsLock)
        {
            foreach (var symbol in symbolList)
            {
                if (_monitoredSymbols.Remove(symbol))
                {
                    removedSymbols.Add(symbol);
                }
            }
        }
        
        foreach (var symbol in removedSymbols)
        {
            _microstructureTrackers.TryRemove(symbol, out _);
        }
        
        if (removedSymbols.Any())
        {
            _logger.LogInformation("Removed {Count} symbols from microstructure pattern detection", removedSymbols.Count);
        }
    }
    
    public async Task<MicrostructureAnalysis?> GetMicrostructureAnalysisAsync(string symbol)
    {
        if (!_microstructureTrackers.TryGetValue(symbol, out var tracker))
            return null;
            
        try
        {
            // Try Redis cache first
            var database = await _redisService.GetDatabaseAsync();
            var cacheKey = string.Format(MicrostructureAnalysisKeyPattern, symbol);
            var cachedData = await database.StringGetAsync(cacheKey);
            
            if (cachedData.HasValue)
            {
                var analysis = JsonSerializer.Deserialize<MicrostructureAnalysis>(cachedData!);
                if (analysis != null && DateTime.UtcNow - analysis.LastUpdate < _config.CacheExpiry)
                {
                    return analysis;
                }
            }
            
            // Calculate current analysis
            return tracker.GetCurrentAnalysis();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting microstructure analysis for {Symbol}", symbol);
            return null;
        }
    }
    
    public async Task<bool> IsFavorableForEntryAsync(string symbol, MicrostructureOrderSide orderSide)
    {
        var analysis = await GetMicrostructureAnalysisAsync(symbol);
        if (analysis == null)
            return false;
            
        return orderSide == MicrostructureOrderSide.Buy ? analysis.IsFavorableForLong : analysis.IsFavorableForShort;
    }
    
    public async Task<decimal> GetPatternStrengthAsync(string symbol)
    {
        var analysis = await GetMicrostructureAnalysisAsync(symbol);
        return analysis?.PatternStrength ?? 0;
    }
    
    public async Task<IEnumerable<MicrostructurePattern>> GetPatternHistoryAsync(string symbol, int minutes = 30)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var historyKey = string.Format(PatternHistoryKeyPattern, symbol);
            var historyData = await database.ListRangeAsync(historyKey, 0, minutes * 2 - 1); // 2 entries per minute
            
            var patternHistory = new List<MicrostructurePattern>();
            foreach (var item in historyData)
            {
                if (item.HasValue)
                {
                    var pattern = JsonSerializer.Deserialize<MicrostructurePattern>(item!);
                    if (pattern != null)
                    {
                        patternHistory.Add(pattern);
                    }
                }
            }
            
            return patternHistory.OrderByDescending(p => p.StartTime);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error getting pattern history for {Symbol}", symbol);
            return Enumerable.Empty<MicrostructurePattern>();
        }
    }
    
    public async Task UpdateConfigurationAsync(MicrostructurePatternConfig config)
    {
        _config = config;
        
        // Update all trackers with new config
        foreach (var tracker in _microstructureTrackers.Values)
        {
            tracker.UpdateConfig(config);
        }
        
        _logger.LogInformation("Microstructure pattern detection configuration updated");
    }
    
    public MicrostructureDetectionStatus GetStatus() => _status;
    
    public IEnumerable<string> GetMonitoredSymbols()
    {
        lock (_symbolsLock)
        {
            return _monitoredSymbols.ToList();
        }
    }
    
    private MicrostructurePatternConfig LoadConfiguration(IConfiguration configuration)
    {
        var section = configuration.GetSection("MicrostructurePattern");
        
        return new MicrostructurePatternConfig(
            TickSequenceLength: section.GetValue("TickSequenceLength", 10),
            MinUptickThreshold: section.GetValue("MinUptickThreshold", 0.01m),
            MinBidSupportRatio: section.GetValue("MinBidSupportRatio", 0.95m),
            MaxSpreadWidening: section.GetValue("MaxSpreadWidening", 2.0m),
            MinLiquidityThreshold: section.GetValue("MinLiquidityThreshold", 1000m),
            RequireVolumeConfirmation: section.GetValue("RequireVolumeConfirmation", true),
            MinConsecutiveTicks: section.GetValue("MinConsecutiveTicks", 3),
            PatternValidityPeriod: TimeSpan.FromMinutes(section.GetValue("PatternValidityMinutes", 2)),
            VolatilityAdjustmentFactor: section.GetValue("VolatilityAdjustmentFactor", 0.5m),
            CacheExpiry: TimeSpan.FromMinutes(section.GetValue("CacheExpiryMinutes", 1))
        );
    }
    
    private async void OnTradeReceived(object? sender, TradeTickEventArgs e)
    {
        if (_disposed || !_microstructureTrackers.TryGetValue(e.TradeTick.Symbol, out var tracker))
            return;

        try
        {
            var analysis = tracker.UpdateWithTrade(e.TradeTick.Price, e.TradeTick.Size, e.TradeTick.Timestamp);
            if (analysis != null)
            {
                await ProcessMicrostructureUpdate(analysis);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing trade tick for microstructure: {Symbol}", e.TradeTick.Symbol);
        }
    }

    private async void OnQuoteReceived(object? sender, QuoteTickEventArgs e)
    {
        if (_disposed || !_microstructureTrackers.TryGetValue(e.QuoteTick.Symbol, out var tracker))
            return;

        try
        {
            var analysis = tracker.UpdateWithQuote(e.QuoteTick.BidPrice, e.QuoteTick.AskPrice,
                e.QuoteTick.BidSize, e.QuoteTick.AskSize, e.QuoteTick.Timestamp);
            if (analysis != null)
            {
                await ProcessMicrostructureUpdate(analysis);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing quote tick for microstructure: {Symbol}", e.QuoteTick.Symbol);
        }
    }

    private async Task ProcessMicrostructureUpdate(MicrostructureAnalysis analysis)
    {
        try
        {
            // Cache analysis in Redis
            await CacheMicrostructureAnalysisAsync(analysis);

            // Check for specific patterns
            await CheckForPatterns(analysis);

            // Check for optimal entry conditions
            if (analysis.Condition == MicrostructureCondition.Favorable)
            {
                var recommendedSide = DetermineOptimalSide(analysis);
                if (recommendedSide.HasValue)
                {
                    OptimalEntryDetected?.Invoke(this, new OptimalEntryEventArgs
                    {
                        Symbol = analysis.Symbol,
                        RecommendedSide = recommendedSide.Value,
                        OptimalPrice = recommendedSide.Value == MicrostructureOrderSide.Buy ? analysis.BidPrice : analysis.AskPrice,
                        PatternStrength = analysis.PatternStrength,
                        PatternType = analysis.ActivePattern,
                        Timestamp = analysis.LastUpdate,
                        Analysis = analysis
                    });

                    _logger.LogInformation("Optimal entry detected for {Symbol}: {Side} at {Price:F2} (strength: {Strength:F1}%)",
                        analysis.Symbol, recommendedSide.Value,
                        recommendedSide.Value == MicrostructureOrderSide.Buy ? analysis.BidPrice : analysis.AskPrice,
                        analysis.PatternStrength);
                }
            }

            // Check for deteriorating conditions
            if (analysis.Condition == MicrostructureCondition.Deteriorating)
            {
                MicrostructureDeterioration?.Invoke(this, new MicrostructureDeteriorationEventArgs
                {
                    Symbol = analysis.Symbol,
                    Reason = "Microstructure conditions deteriorating",
                    CurrentPrice = analysis.CurrentPrice,
                    Timestamp = analysis.LastUpdate
                });

                _logger.LogWarning("Microstructure deterioration detected for {Symbol}: {Price:F2}",
                    analysis.Symbol, analysis.CurrentPrice);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error processing microstructure update for {Symbol}", analysis.Symbol);
        }
    }

    private async Task CheckForPatterns(MicrostructureAnalysis analysis)
    {
        // Check for uptick + bid support pattern
        if (analysis.ActivePattern == PatternType.UptickBidSupport &&
            analysis.ConsecutiveUpticks >= _config.MinConsecutiveTicks &&
            analysis.BidSupportRatio >= _config.MinBidSupportRatio)
        {
            UptickBidSupportDetected?.Invoke(this, new UptickBidSupportEventArgs
            {
                Symbol = analysis.Symbol,
                TriggerPrice = analysis.CurrentPrice,
                BidPrice = analysis.BidPrice,
                BidSupportRatio = analysis.BidSupportRatio,
                ConsecutiveUpticks = analysis.ConsecutiveUpticks,
                PatternStrength = analysis.PatternStrength,
                Timestamp = analysis.LastUpdate,
                Analysis = analysis
            });

            await RecordPatternAsync(new MicrostructurePattern(
                Symbol: analysis.Symbol,
                Type: PatternType.UptickBidSupport,
                TriggerPrice: analysis.CurrentPrice,
                PatternStrength: analysis.PatternStrength,
                TickSequenceLength: analysis.ConsecutiveUpticks,
                StartTime: analysis.LastUpdate.AddSeconds(-analysis.ConsecutiveUpticks),
                EndTime: analysis.LastUpdate,
                Duration: TimeSpan.FromSeconds(analysis.ConsecutiveUpticks),
                WasSuccessful: true
            ));

            _logger.LogInformation("Uptick + bid support pattern detected for {Symbol}: {Upticks} upticks, {BidSupport:F2}% bid support",
                analysis.Symbol, analysis.ConsecutiveUpticks, analysis.BidSupportRatio * 100);
        }

        // Check for down-tick + spread widening pattern
        if (analysis.ActivePattern == PatternType.DowntickSpreadWidening &&
            analysis.ConsecutiveDownticks >= _config.MinConsecutiveTicks &&
            analysis.SpreadPercent >= _config.MaxSpreadWidening)
        {
            DowntickSpreadWideningDetected?.Invoke(this, new DowntickSpreadWideningEventArgs
            {
                Symbol = analysis.Symbol,
                TriggerPrice = analysis.CurrentPrice,
                SpreadWidening = analysis.SpreadPercent,
                ConsecutiveDownticks = analysis.ConsecutiveDownticks,
                PatternStrength = analysis.PatternStrength,
                Timestamp = analysis.LastUpdate,
                Analysis = analysis
            });

            await RecordPatternAsync(new MicrostructurePattern(
                Symbol: analysis.Symbol,
                Type: PatternType.DowntickSpreadWidening,
                TriggerPrice: analysis.CurrentPrice,
                PatternStrength: analysis.PatternStrength,
                TickSequenceLength: analysis.ConsecutiveDownticks,
                StartTime: analysis.LastUpdate.AddSeconds(-analysis.ConsecutiveDownticks),
                EndTime: analysis.LastUpdate,
                Duration: TimeSpan.FromSeconds(analysis.ConsecutiveDownticks),
                WasSuccessful: true
            ));

            _logger.LogInformation("Down-tick + spread widening pattern detected for {Symbol}: {Downticks} downticks, {Spread:F2}% spread",
                analysis.Symbol, analysis.ConsecutiveDownticks, analysis.SpreadPercent);
        }
    }

    private MicrostructureOrderSide? DetermineOptimalSide(MicrostructureAnalysis analysis)
    {
        if (analysis.IsFavorableForLong && analysis.PatternStrength >= 70)
            return MicrostructureOrderSide.Buy;
        if (analysis.IsFavorableForShort && analysis.PatternStrength >= 70)
            return MicrostructureOrderSide.Sell;
        return null;
    }

    private async Task CacheMicrostructureAnalysisAsync(MicrostructureAnalysis analysis)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var key = string.Format(MicrostructureAnalysisKeyPattern, analysis.Symbol);
            var json = JsonSerializer.Serialize(analysis);
            await database.StringSetAsync(key, json, _config.CacheExpiry);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error caching microstructure analysis for {Symbol}", analysis.Symbol);
        }
    }

    private async Task RecordPatternAsync(MicrostructurePattern pattern)
    {
        try
        {
            var database = await _redisService.GetDatabaseAsync();
            var historyKey = string.Format(PatternHistoryKeyPattern, pattern.Symbol);
            var json = JsonSerializer.Serialize(pattern);

            await database.ListLeftPushAsync(historyKey, json);
            await database.ListTrimAsync(historyKey, 0, 120 - 1); // Keep 2 hours of patterns
            await database.KeyExpireAsync(historyKey, TimeSpan.FromHours(4));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error recording pattern for {Symbol}", pattern.Symbol);
        }
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _disposed = true;

        try
        {
            _tickStreamService.TradeReceived -= OnTradeReceived;
            _tickStreamService.QuoteReceived -= OnQuoteReceived;

            _microstructureTrackers.Clear();

            lock (_symbolsLock)
            {
                _monitoredSymbols.Clear();
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error disposing MicrostructurePatternDetector");
        }
    }
}
