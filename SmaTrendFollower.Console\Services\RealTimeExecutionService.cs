using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using SmaTrendFollower.Extensions;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Diagnostics;
using SmaOrderType = SmaTrendFollower.Models.SmaOrderType;
using SmaOrderSide = SmaTrendFollower.Models.SmaOrderSide;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time execution service implementation
/// Combines trade ticks + quote spread width + VWAP for optimal execution decisions
/// Chooses limit vs market dynamically and throttles trade entry during high bid-ask instability or spread spikes
/// </summary>
public sealed class RealTimeExecutionService : IRealTimeExecutionService, IDisposable
{
    private readonly ITickStreamService _tickStreamService;
    private readonly IVWAPMonitorService _vwapMonitorService;
    private readonly IOptimizedRedisConnectionService _redisService;
    private readonly IMarketDataService _marketDataService;
    private readonly ILogger<RealTimeExecutionService> _logger;
    private readonly RealTimeExecutionConfig _config;
    
    private readonly Timer _monitoringTimer;
    private readonly ConcurrentDictionary<string, MarketMicrostructureAnalysis> _microstructureData = new();
    private readonly ConcurrentDictionary<string, ExecutionThrottleStatus> _throttleStatus = new();
    private readonly ConcurrentDictionary<string, ExecutionParameters> _executionParameters = new();
    private readonly ConcurrentQueue<ExecutionQualityMetrics> _executionHistory = new();
    private readonly SemaphoreSlim _analysisLock = new(1, 1);
    
    private readonly HashSet<string> _monitoredSymbols = new();
    private readonly object _symbolsLock = new();
    
    private RealTimeExecutionStatus _status = RealTimeExecutionStatus.Stopped;
    private DateTime _monitoringStartedAt;
    private int _totalExecutions;
    private int _throttledExecutions;
    private bool _disposed;

    public RealTimeExecutionService(
        ITickStreamService tickStreamService,
        IVWAPMonitorService vwapMonitorService,
        IOptimizedRedisConnectionService redisService,
        IMarketDataService marketDataService,
        IConfiguration configuration,
        ILogger<RealTimeExecutionService> logger)
    {
        _tickStreamService = tickStreamService ?? throw new ArgumentNullException(nameof(tickStreamService));
        _vwapMonitorService = vwapMonitorService ?? throw new ArgumentNullException(nameof(vwapMonitorService));
        _redisService = redisService ?? throw new ArgumentNullException(nameof(redisService));
        _marketDataService = marketDataService ?? throw new ArgumentNullException(nameof(marketDataService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        _config = new RealTimeExecutionConfig();
        configuration.GetSection("RealTimeExecution").Bind(_config);

        // Set up monitoring timer
        _monitoringTimer = new Timer(MonitoringCallback, null, Timeout.Infinite, Timeout.Infinite);
    }

    // === Events ===
    
    public event EventHandler<ExecutionDecisionEventArgs>? ExecutionDecisionMade;
    public event EventHandler<ExecutionThrottledEventArgs>? ExecutionThrottled;
    public event EventHandler<SpreadSpikeEventArgs>? SpreadSpikeDetected;

#pragma warning disable CS0067 // Event is never used - reserved for future implementation
    public event EventHandler<ExecutionConditionsChangedEventArgs>? ExecutionConditionsChanged;
#pragma warning restore CS0067

    // === Core Methods ===

    public async Task StartMonitoringAsync(IEnumerable<string> symbols, CancellationToken cancellationToken = default)
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(RealTimeExecutionService));

        if (_status == RealTimeExecutionStatus.Active)
            return;

        try
        {
            _status = RealTimeExecutionStatus.Starting;
            _logger.LogInformation("Starting RealTimeExecutionService for {Count} symbols", symbols.Count());

            _monitoringStartedAt = DateTime.UtcNow;

            // Store monitored symbols
            lock (_symbolsLock)
            {
                _monitoredSymbols.Clear();
                foreach (var symbol in symbols)
                {
                    _monitoredSymbols.Add(symbol);
                    
                    // Initialize default execution parameters
                    _executionParameters.TryAdd(symbol, new ExecutionParameters());
                }
            }

            // Subscribe to tick stream events
            await SubscribeToTickEventsAsync();

            // Start monitoring timer
            _monitoringTimer.Change(
                TimeSpan.FromMilliseconds(_config.MonitoringIntervalMs),
                TimeSpan.FromMilliseconds(_config.MonitoringIntervalMs));

            _status = RealTimeExecutionStatus.Active;
            _logger.LogInformation("RealTimeExecutionService started successfully");
        }
        catch (Exception ex)
        {
            _status = RealTimeExecutionStatus.Error;
            _logger.LogError(ex, "Failed to start RealTimeExecutionService");
            throw;
        }
    }

    public async Task StopMonitoringAsync(CancellationToken cancellationToken = default)
    {
        if (_disposed || _status == RealTimeExecutionStatus.Stopped)
            return;

        try
        {
            _logger.LogInformation("Stopping RealTimeExecutionService...");

            // Stop monitoring timer
            _monitoringTimer.Change(Timeout.Infinite, Timeout.Infinite);

            // Unsubscribe from tick events
            await UnsubscribeFromTickEventsAsync();

            // Clear monitored symbols
            lock (_symbolsLock)
            {
                _monitoredSymbols.Clear();
            }

            _status = RealTimeExecutionStatus.Stopped;
            _logger.LogInformation("RealTimeExecutionService stopped");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping RealTimeExecutionService");
        }
    }

    public async Task<ExecutionStrategy> EvaluateExecutionStrategyAsync(
        string symbol,
        SmaOrderSide side,
        decimal quantity,
        CancellationToken cancellationToken = default)
    {
        await _analysisLock.WaitAsync(cancellationToken);
        try
        {
            var stopwatch = Stopwatch.StartNew();
            _logger.LogDebug("Evaluating execution strategy for {Symbol} {Side} {Quantity}", symbol, side, quantity);

            // Check if execution is currently allowed
            var isAllowed = await IsExecutionAllowedAsync(symbol, cancellationToken);
            if (!isAllowed)
            {
                return new ExecutionStrategy
                {
                    Symbol = symbol,
                    RecommendedOrderType = SmaOrderType.Limit,
                    Timing = ExecutionTiming.Patient,
                    Urgency = ExecutionUrgency.Low,
                    ConfidenceScore = 0.1m,
                    Reasoning = "Execution currently throttled or not allowed"
                };
            }

            // Get current market analysis
            var microstructure = await GetMicrostructureAnalysisAsync(symbol, cancellationToken);
            var spreadAnalysis = await GetSpreadAnalysisAsync(symbol, cancellationToken);
            var vwapAnalysis = await GetVwapExecutionAnalysisAsync(symbol, cancellationToken);
            var liquidityAssessment = await GetLiquidityAssessmentAsync(symbol, cancellationToken);

            // Determine optimal execution strategy
            var strategy = DetermineOptimalStrategy(
                symbol, side, quantity, microstructure, spreadAnalysis, vwapAnalysis, liquidityAssessment);

            stopwatch.Stop();

            ExecutionDecisionMade?.Invoke(this, new ExecutionDecisionEventArgs
            {
                Symbol = symbol,
                Strategy = strategy,
                DecisionMadeAt = DateTime.UtcNow,
                AnalysisTime = stopwatch.Elapsed
            });

            _logger.LogDebug("Execution strategy for {Symbol}: {OrderType} at {Price} (Confidence: {Confidence:P1})",
                symbol, strategy.RecommendedOrderType, strategy.LimitPrice, strategy.ConfidenceScore);

            return strategy;
        }
        finally
        {
            _analysisLock.Release();
        }
    }

    public async Task<bool> IsExecutionAllowedAsync(string symbol, CancellationToken cancellationToken = default)
    {
        // Check if symbol is throttled
        if (_throttleStatus.TryGetValue(symbol, out var throttleStatus))
        {
            if (throttleStatus.IsThrottled && DateTime.UtcNow < throttleStatus.ExpiresAt)
            {
                return false;
            }
            else if (DateTime.UtcNow >= throttleStatus.ExpiresAt)
            {
                // Throttle has expired, remove it
                _throttleStatus.TryRemove(symbol, out _);
            }
        }

        // Check spread conditions
        var spreadAnalysis = await GetSpreadAnalysisAsync(symbol, cancellationToken);
        if (spreadAnalysis.IsSpreadSpike)
        {
            _logger.LogWarning("Execution blocked for {Symbol} due to spread spike: {Spread:F4}", 
                symbol, spreadAnalysis.CurrentSpread);
            return false;
        }

        // Check liquidity conditions
        var liquidityAssessment = await GetLiquidityAssessmentAsync(symbol, cancellationToken);
        if (liquidityAssessment.Level == LiquidityLevel.VeryLow)
        {
            _logger.LogWarning("Execution blocked for {Symbol} due to very low liquidity", symbol);
            return false;
        }

        return true;
    }

    public async Task<decimal?> GetOptimalExecutionPriceAsync(
        string symbol,
        SmaOrderSide side,
        decimal quantity,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var microstructure = await GetMicrostructureAnalysisAsync(symbol, cancellationToken);
            var vwapAnalysis = await GetVwapExecutionAnalysisAsync(symbol, cancellationToken);

            if (microstructure.BidPrice == 0 || microstructure.AskPrice == 0)
                return null;

            // Calculate optimal price based on side and market conditions
            decimal optimalPrice;
            
            if (side == SmaOrderSide.Buy)
            {
                // For buy orders, consider bid price and VWAP
                optimalPrice = Math.Min(microstructure.BidPrice + microstructure.Spread * 0.3m, vwapAnalysis.CurrentVwap);
            }
            else
            {
                // For sell orders, consider ask price and VWAP
                optimalPrice = Math.Max(microstructure.AskPrice - microstructure.Spread * 0.3m, vwapAnalysis.CurrentVwap);
            }

            return optimalPrice;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating optimal execution price for {Symbol}", symbol);
            return null;
        }
    }

    // === Analysis Methods ===

    public async Task<MarketMicrostructureAnalysis> GetMicrostructureAnalysisAsync(
        string symbol, 
        CancellationToken cancellationToken = default)
    {
        if (_microstructureData.TryGetValue(symbol, out var cached))
        {
            var age = DateTime.UtcNow - cached.AnalyzedAt;
            if (age.TotalSeconds < 30) // Use cached data if less than 30 seconds old
            {
                return cached;
            }
        }

        try
        {
            // Get latest quote data from tick stream or market data service
            var endTime = DateTime.UtcNow;
            var startTime = endTime.AddMinutes(-5);
            var bars = await _marketDataService.GetStockBarsAsync(symbol, startTime, endTime);
            var latestBar = bars.Items.LastOrDefault();

            if (latestBar == null)
            {
                return new MarketMicrostructureAnalysis
                {
                    Symbol = symbol,
                    AnalyzedAt = DateTime.UtcNow,
                    Quality = MicrostructureQuality.VeryPoor,
                    LiquidityLevel = LiquidityLevel.VeryLow
                };
            }

            // Simulate bid/ask from bar data (in real implementation, would use actual quote data)
            var midPrice = (latestBar.High + latestBar.Low) / 2;
            var spread = (latestBar.High - latestBar.Low) * 0.1m; // Estimate spread as 10% of range
            var bidPrice = midPrice - spread / 2;
            var askPrice = midPrice + spread / 2;

            var analysis = new MarketMicrostructureAnalysis
            {
                Symbol = symbol,
                AnalyzedAt = DateTime.UtcNow,
                BidPrice = bidPrice,
                AskPrice = askPrice,
                Spread = spread,
                SpreadPercent = (spread / midPrice) * 100,
                BidSize = latestBar.Volume * 0.3m, // Estimate
                AskSize = latestBar.Volume * 0.3m, // Estimate
                OrderBookImbalance = 0, // Would calculate from real order book data
                TickDirection = latestBar.Close > latestBar.Open ? 1 : -1,
                RecentTradeCount = 10, // Estimate
                AverageTradeSize = latestBar.Volume / 10, // Estimate
                Quality = ClassifyMicrostructureQuality(spread, midPrice),
                LiquidityLevel = ClassifyLiquidityLevel(latestBar.Volume)
            };

            _microstructureData.AddOrUpdate(symbol, analysis, (_, _) => analysis);
            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing microstructure for {Symbol}", symbol);
            return new MarketMicrostructureAnalysis
            {
                Symbol = symbol,
                AnalyzedAt = DateTime.UtcNow,
                Quality = MicrostructureQuality.VeryPoor,
                LiquidityLevel = LiquidityLevel.VeryLow
            };
        }
    }

    public async Task<SpreadAnalysis> GetSpreadAnalysisAsync(string symbol, CancellationToken cancellationToken = default)
    {
        try
        {
            var microstructure = await GetMicrostructureAnalysisAsync(symbol, cancellationToken);
            
            // Get historical spread data (simplified)
            var currentSpread = microstructure.Spread;
            var averageSpread5Min = currentSpread * 1.1m; // Simulate historical average
            var averageSpread1Hour = currentSpread * 1.2m;
            var spreadVolatility = currentSpread * 0.3m;

            var isSpreadSpike = currentSpread > averageSpread5Min * _config.SpreadSpikeThreshold;

            if (isSpreadSpike)
            {
                SpreadSpikeDetected?.Invoke(this, new SpreadSpikeEventArgs
                {
                    Symbol = symbol,
                    CurrentSpread = currentSpread,
                    NormalSpread = averageSpread5Min,
                    SpikeMultiplier = currentSpread / averageSpread5Min,
                    DetectedAt = DateTime.UtcNow
                });
            }

            return new SpreadAnalysis
            {
                Symbol = symbol,
                AnalyzedAt = DateTime.UtcNow,
                CurrentSpread = currentSpread,
                AverageSpread5Min = averageSpread5Min,
                AverageSpread1Hour = averageSpread1Hour,
                SpreadVolatility = spreadVolatility,
                SpreadPercentile = 50m, // Would calculate from historical data
                IsSpreadSpike = isSpreadSpike,
                Trend = ClassifySpreadTrend(currentSpread, averageSpread5Min),
                Stability = ClassifySpreadStability(spreadVolatility, currentSpread)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing spread for {Symbol}", symbol);
            return new SpreadAnalysis
            {
                Symbol = symbol,
                AnalyzedAt = DateTime.UtcNow,
                Trend = SpreadTrend.Stable,
                Stability = SpreadStability.Moderate
            };
        }
    }

    public async Task<VwapExecutionAnalysis> GetVwapExecutionAnalysisAsync(
        string symbol, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            var vwapData = await _vwapMonitorService.GetCurrentVWAPAsync(symbol);
            var microstructure = await GetMicrostructureAnalysisAsync(symbol, cancellationToken);

            if (vwapData == null)
            {
                return new VwapExecutionAnalysis
                {
                    Symbol = symbol,
                    AnalyzedAt = DateTime.UtcNow,
                    Recommendation = VwapExecutionRecommendation.ExecuteImmediately,
                    OptimalTiming = ExecutionTiming.Immediate
                };
            }

            var currentPrice = (microstructure.BidPrice + microstructure.AskPrice) / 2;
            var vwapDeviation = ((currentPrice - vwapData.VWAP) / vwapData.VWAP) * 100;

            var recommendation = DetermineVwapRecommendation(currentPrice, vwapData.VWAP, vwapDeviation);
            var optimalTiming = DetermineOptimalTiming(vwapDeviation, microstructure.Quality);

            return new VwapExecutionAnalysis
            {
                Symbol = symbol,
                AnalyzedAt = DateTime.UtcNow,
                CurrentVwap = vwapData.VWAP,
                CurrentPrice = currentPrice,
                VwapDeviation = vwapDeviation,
                VwapTrend = 0, // Would calculate trend from historical VWAP data
                Recommendation = recommendation,
                OptimalExecutionPrice = CalculateOptimalVwapPrice(currentPrice, vwapData.VWAP, recommendation),
                OptimalTiming = optimalTiming
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing VWAP execution for {Symbol}", symbol);
            return new VwapExecutionAnalysis
            {
                Symbol = symbol,
                AnalyzedAt = DateTime.UtcNow,
                Recommendation = VwapExecutionRecommendation.ExecuteImmediately,
                OptimalTiming = ExecutionTiming.Immediate
            };
        }
    }

    public async Task<ExecutionQualityMetrics> GetExecutionQualityMetricsAsync(
        string symbol,
        CancellationToken cancellationToken = default)
    {
        // Return simplified metrics (in real implementation, would track actual execution history)
        return new ExecutionQualityMetrics(
            symbol,
            100, // TotalExecutions
            2.5m, // AverageSlippage (bps)
            2.0m, // MedianSlippage
            5.0m, // MaxSlippage
            1.5m, // AverageExecutionTime (seconds)
            95.0m, // SuccessRate
            new Dictionary<ExecutionQualityRating, int>
            {
                { ExecutionQualityRating.Excellent, 20 },
                { ExecutionQualityRating.Good, 60 },
                { ExecutionQualityRating.Fair, 15 },
                { ExecutionQualityRating.Poor, 4 },
                { ExecutionQualityRating.Failed, 1 }
            }
        );
    }

    public async Task<LiquidityAssessment> GetLiquidityAssessmentAsync(
        string symbol,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var microstructure = await GetMicrostructureAnalysisAsync(symbol, cancellationToken);

            var totalLiquidity = microstructure.BidSize + microstructure.AskSize;
            var liquidityScore = Math.Min(1.0m, totalLiquidity / _config.LiquidityThreshold);

            return new LiquidityAssessment
            {
                Symbol = symbol,
                AssessedAt = DateTime.UtcNow,
                BidLiquidity = microstructure.BidSize,
                AskLiquidity = microstructure.AskSize,
                TotalLiquidity = totalLiquidity,
                LiquidityScore = liquidityScore,
                Level = ClassifyLiquidityLevel(totalLiquidity),
                EstimatedMarketImpact = CalculateEstimatedMarketImpact(totalLiquidity),
                MaxRecommendedSize = totalLiquidity * 0.1m, // 10% of available liquidity
                Trend = LiquidityTrend.Stable
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assessing liquidity for {Symbol}", symbol);
            return new LiquidityAssessment
            {
                Symbol = symbol,
                AssessedAt = DateTime.UtcNow,
                Level = LiquidityLevel.VeryLow,
                Trend = LiquidityTrend.Stable
            };
        }
    }

    // === Execution Control ===

    public async Task ThrottleExecutionAsync(string symbol, TimeSpan duration, string reason)
    {
        var throttleStatus = new ExecutionThrottleStatus
        {
            Symbol = symbol,
            IsThrottled = true,
            ThrottledAt = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.Add(duration),
            Reason = reason,
            ThrottleType = ThrottleType.Manual,
            RemainingDuration = duration
        };

        _throttleStatus.AddOrUpdate(symbol, throttleStatus, (_, _) => throttleStatus);
        _throttledExecutions++;

        ExecutionThrottled?.Invoke(this, new ExecutionThrottledEventArgs
        {
            Symbol = symbol,
            ThrottleType = ThrottleType.Manual,
            Duration = duration,
            Reason = reason,
            ThrottledAt = DateTime.UtcNow
        });

        _logger.LogWarning("Execution throttled for {Symbol} for {Duration}: {Reason}", symbol, duration, reason);
    }

    public async Task RemoveExecutionThrottleAsync(string symbol)
    {
        if (_throttleStatus.TryRemove(symbol, out var throttleStatus))
        {
            _logger.LogInformation("Execution throttle removed for {Symbol}", symbol);
        }
    }

    public async Task<ExecutionThrottleStatus?> GetExecutionThrottleStatusAsync(string symbol)
    {
        _throttleStatus.TryGetValue(symbol, out var status);
        return status;
    }

    public async Task UpdateExecutionParametersAsync(string symbol, ExecutionParameters parameters)
    {
        _executionParameters.AddOrUpdate(symbol, parameters, (_, _) => parameters);
        _logger.LogDebug("Updated execution parameters for {Symbol}", symbol);
    }

    // === Status and Configuration ===

    public RealTimeExecutionStatus GetStatus() => _status;

    public IEnumerable<string> GetMonitoredSymbols()
    {
        lock (_symbolsLock)
        {
            return _monitoredSymbols.ToList();
        }
    }

    public async Task UpdateConfigurationAsync(RealTimeExecutionConfig config)
    {
        // Update configuration (implementation would update _config)
        await Task.CompletedTask;
    }

    public async Task<ExecutionStatistics> GetExecutionStatisticsAsync()
    {
        var monitoringDuration = _status == RealTimeExecutionStatus.Active ?
            DateTime.UtcNow - _monitoringStartedAt : TimeSpan.Zero;

        return new ExecutionStatistics
        {
            LastUpdated = DateTime.UtcNow,
            TotalExecutions = _totalExecutions,
            ThrottledExecutions = _throttledExecutions,
            AverageSlippage = 2.5m, // Would calculate from actual execution history
            AverageExecutionTime = 1.8m, // seconds
            MonitoringDuration = monitoringDuration
        };
    }

    // === Private Helper Methods ===

    private async void MonitoringCallback(object? state)
    {
        try
        {
            await PerformPeriodicMonitoringAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in monitoring callback");
        }
    }

    private async Task PerformPeriodicMonitoringAsync()
    {
        lock (_symbolsLock)
        {
            if (_monitoredSymbols.Count == 0)
                return;
        }

        // Update microstructure data for all monitored symbols
        var symbols = GetMonitoredSymbols().ToList();
        var tasks = symbols.Select(async symbol =>
        {
            try
            {
                await GetMicrostructureAnalysisAsync(symbol);
                await GetSpreadAnalysisAsync(symbol);
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Error updating data for {Symbol}", symbol);
            }
        });

        await Task.WhenAll(tasks);
    }

    private async Task SubscribeToTickEventsAsync()
    {
        // Subscribe to tick stream events for real-time updates
        // Implementation would subscribe to trade and quote events
        await Task.CompletedTask;
    }

    private async Task UnsubscribeFromTickEventsAsync()
    {
        // Unsubscribe from tick stream events
        await Task.CompletedTask;
    }

    private ExecutionStrategy DetermineOptimalStrategy(
        string symbol,
        SmaOrderSide side,
        decimal quantity,
        MarketMicrostructureAnalysis microstructure,
        SpreadAnalysis spreadAnalysis,
        VwapExecutionAnalysis vwapAnalysis,
        LiquidityAssessment liquidityAssessment)
    {
        var strategy = new ExecutionStrategy { Symbol = symbol };

        // Determine order type based on market conditions
        if (spreadAnalysis.IsSpreadSpike || liquidityAssessment.Level == LiquidityLevel.VeryLow)
        {
            strategy.RecommendedOrderType = SmaOrderType.Limit;
            strategy.Timing = ExecutionTiming.Patient;
            strategy.Urgency = ExecutionUrgency.Low;
            strategy.ConfidenceScore = 0.3m;
            strategy.Reasoning = "Using limit order due to poor market conditions";
        }
        else if (microstructure.Quality >= MicrostructureQuality.Good && !spreadAnalysis.IsSpreadSpike)
        {
            strategy.RecommendedOrderType = SmaOrderType.Market;
            strategy.Timing = ExecutionTiming.Immediate;
            strategy.Urgency = ExecutionUrgency.High;
            strategy.ConfidenceScore = 0.8m;
            strategy.Reasoning = "Using market order due to good market conditions";
        }
        else
        {
            strategy.RecommendedOrderType = SmaOrderType.Limit;
            strategy.Timing = ExecutionTiming.Opportunistic;
            strategy.Urgency = ExecutionUrgency.Normal;
            strategy.ConfidenceScore = 0.6m;
            strategy.Reasoning = "Using limit order for balanced execution";
        }

        // Set limit price if using limit order
        if (strategy.RecommendedOrderType == SmaOrderType.Limit)
        {
            strategy.LimitPrice = side == SmaOrderSide.Buy ?
                microstructure.BidPrice + microstructure.Spread * 0.2m :
                microstructure.AskPrice - microstructure.Spread * 0.2m;
        }

        return strategy;
    }

    private static MicrostructureQuality ClassifyMicrostructureQuality(decimal spread, decimal price)
    {
        var spreadPercent = (spread / price) * 100;
        return spreadPercent switch
        {
            < 0.05m => MicrostructureQuality.Excellent,
            < 0.1m => MicrostructureQuality.Good,
            < 0.2m => MicrostructureQuality.Fair,
            < 0.5m => MicrostructureQuality.Poor,
            _ => MicrostructureQuality.VeryPoor
        };
    }

    private static LiquidityLevel ClassifyLiquidityLevel(decimal volume)
    {
        return volume switch
        {
            > 100000m => LiquidityLevel.VeryHigh,
            > 50000m => LiquidityLevel.High,
            > 10000m => LiquidityLevel.Normal,
            > 1000m => LiquidityLevel.Low,
            _ => LiquidityLevel.VeryLow
        };
    }

    private static SpreadTrend ClassifySpreadTrend(decimal currentSpread, decimal averageSpread)
    {
        var ratio = currentSpread / averageSpread;
        return ratio switch
        {
            > 1.1m => SpreadTrend.Widening,
            < 0.9m => SpreadTrend.Tightening,
            _ => SpreadTrend.Stable
        };
    }

    private static SpreadStability ClassifySpreadStability(decimal spreadVolatility, decimal currentSpread)
    {
        var volatilityRatio = spreadVolatility / currentSpread;
        return volatilityRatio switch
        {
            < 0.1m => SpreadStability.VeryStable,
            < 0.2m => SpreadStability.Stable,
            < 0.4m => SpreadStability.Moderate,
            < 0.6m => SpreadStability.Unstable,
            _ => SpreadStability.VeryUnstable
        };
    }

    private static VwapExecutionRecommendation DetermineVwapRecommendation(
        decimal currentPrice,
        decimal vwap,
        decimal vwapDeviation)
    {
        return Math.Abs(vwapDeviation) switch
        {
            < 0.1m => VwapExecutionRecommendation.ExecuteAtVwap,
            < 0.5m when vwapDeviation > 0 => VwapExecutionRecommendation.ExecuteBelowVwap,
            < 0.5m when vwapDeviation < 0 => VwapExecutionRecommendation.ExecuteAboveVwap,
            < 1.0m => VwapExecutionRecommendation.WaitForBetterPrice,
            _ => VwapExecutionRecommendation.ExecuteImmediately
        };
    }

    private static ExecutionTiming DetermineOptimalTiming(decimal vwapDeviation, MicrostructureQuality quality)
    {
        if (quality == MicrostructureQuality.VeryPoor)
            return ExecutionTiming.Patient;

        return Math.Abs(vwapDeviation) switch
        {
            < 0.2m => ExecutionTiming.Immediate,
            < 0.5m => ExecutionTiming.Opportunistic,
            _ => ExecutionTiming.Patient
        };
    }

    private static decimal CalculateOptimalVwapPrice(
        decimal currentPrice,
        decimal vwap,
        VwapExecutionRecommendation recommendation)
    {
        return recommendation switch
        {
            VwapExecutionRecommendation.ExecuteAtVwap => vwap,
            VwapExecutionRecommendation.ExecuteAboveVwap => vwap * 1.001m,
            VwapExecutionRecommendation.ExecuteBelowVwap => vwap * 0.999m,
            _ => currentPrice
        };
    }

    private static decimal CalculateEstimatedMarketImpact(decimal totalLiquidity)
    {
        // Simplified market impact calculation (bps)
        return totalLiquidity switch
        {
            > 100000m => 0.5m,
            > 50000m => 1.0m,
            > 10000m => 2.0m,
            > 1000m => 5.0m,
            _ => 10.0m
        };
    }

    public void Dispose()
    {
        if (_disposed)
            return;

        _monitoringTimer?.Dispose();
        _analysisLock?.Dispose();
        _disposed = true;
    }
}
